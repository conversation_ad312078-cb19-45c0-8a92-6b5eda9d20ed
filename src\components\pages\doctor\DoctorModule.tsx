'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export type Doctor = {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    specialty?: string;
    contact?: string;
};

// Sample doctor data - in production this would come from API
const doctorData: Doctor[] = [
    { id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'joh<PERSON><PERSON>', specialty: 'Cardiology', contact: '123-456-7890' },
    { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', specialty: 'Dermatology', contact: '123-456-7891' },
    { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'm<PERSON><PERSON><PERSON>', specialty: 'Pediatrics', contact: '123-456-7892' },
    { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'edavi<PERSON>', specialty: 'Orthopedics', contact: '123-456-7893' },
    { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', specialty: 'Neurology', contact: '123-456-7894' },
];

interface DoctorModuleProps {
    mode?: 'add' | 'edit' | 'view';
    id?: string;
}

const DoctorModule: React.FC<DoctorModuleProps> = ({ mode = 'add', id }) => {
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [doctor, setDoctor] = useState<Doctor | null>(null);
    const [formData, setFormData] = useState<Omit<Doctor, 'id'>>({
        first_name: '',
        last_name: '',
        email: '',
        username: '',
        specialty: '',
        contact: '',
    });

    // Load doctor data for edit and view modes
    useEffect(() => {
        if ((mode === 'edit' || mode === 'view') && id) {
            setLoading(true);
            const doctorId = parseInt(id);
            const foundDoctor = doctorData.find((d) => d.id === doctorId);

            if (foundDoctor) {
                setDoctor(foundDoctor);
                if (mode === 'edit') {
                    setFormData({
                        first_name: foundDoctor.first_name,
                        last_name: foundDoctor.last_name,
                        email: foundDoctor.email,
                        username: foundDoctor.username,
                        specialty: foundDoctor.specialty || '',
                        contact: foundDoctor.contact || '',
                    });
                }
            } else {
                setError('Doctor not found');
                setTimeout(() => router.push('/doctor'), 2000);
            }
            setLoading(false);
        }
    }, [mode, id, router]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            if (mode === 'add') {
                // In production, this would be an API call
                console.log('Creating new doctor:', formData);
                // Simulate API call
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Show success message and redirect
                alert('Doctor created successfully!');
                router.push('/doctor');
            } else {
                // In production, this would be an API call
                console.log('Updating doctor:', { id, ...formData });
                // Simulate API call
                await new Promise((resolve) => setTimeout(resolve, 1000));

                // Show success message and redirect
                alert('Doctor updated successfully!');
                router.push('/doctor');
            }
        } catch (err) {
            setError('An error occurred while saving the doctor');
            console.error('Error saving doctor:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        router.push('/doctor');
    };

    const handleEdit = () => {
        router.push(`/doctor/${id}/edit`);
    };

    const handleBack = () => {
        router.push('/doctor');
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[200px]">
                <div className="text-lg">Loading doctor details...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="text-red-600 text-center">
                    <h2 className="text-xl font-bold mb-2">Error</h2>
                    <p>{error}</p>
                    <p>Redirecting to doctor list...</p>
                </div>
            </div>
        );
    }

    // View Mode Rendering
    if (mode === 'view' && doctor) {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Doctor Details</h2>
                    <div className="flex gap-2">
                        <button type="button" onClick={handleEdit} className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90">
                            Edit Doctor
                        </button>
                        <button type="button" onClick={handleBack} className="bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90">
                            Back to List
                        </button>
                    </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">First Name</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.first_name}</div>
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Last Name</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.last_name}</div>
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Email</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.email}</div>
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Username</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.username}</div>
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Specialty</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.specialty || 'Not specified'}</div>
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Contact</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.contact || 'Not specified'}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mx-auto bg-white p-6 rounded shadow-md">
            <h2 className="text-2xl font-bold mb-6">{mode === 'add' ? 'Add Doctor' : 'Edit Doctor'}</h2>

            <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block mb-1 font-semibold">First Name</label>
                        <input
                            type="text"
                            name="first_name"
                            value={formData.first_name}
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            required
                            placeholder="Enter first name"
                            disabled={loading}
                        />
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold">Last Name</label>
                        <input
                            type="text"
                            name="last_name"
                            value={formData.last_name}
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            required
                            placeholder="Enter last name"
                            disabled={loading}
                        />
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold">Email</label>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            required
                            placeholder="Enter email"
                            disabled={loading}
                        />
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold">Username</label>
                        <input
                            type="text"
                            name="username"
                            value={formData.username}
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            required
                            placeholder="Enter username"
                            disabled={loading}
                        />
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold">Specialty</label>
                        <input
                            type="text"
                            name="specialty"
                            value={formData.specialty}
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                            placeholder="Enter specialty (optional)"
                        />
                    </div>

                    <div>
                        <label className="block mb-1 font-semibold">Contact</label>
                        <input
                            type="text"
                            name="contact"
                            value={formData.contact}
                            placeholder="Enter contact (optional)"
                            onChange={handleInputChange}
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                        />
                    </div>
                </div>

                <div className="flex gap-4 pt-4">
                    <button type="submit" disabled={loading} className="bg-[#EB6309] text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed">
                        {loading ? 'Saving...' : mode === 'add' ? 'Create Doctor' : 'Update Doctor'}
                    </button>

                    <button
                        type="button"
                        onClick={handleCancel}
                        disabled={loading}
                        className="bg-gray-500 text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default DoctorModule;
